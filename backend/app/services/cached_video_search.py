"""
DEPRECATED: This service has been consolidated into unified_video_search.py

This file is kept for reference but should not be used.
Use unified_video_search.py instead for all video search functionality.

Original description:
High-Performance Cached Video Search Service with Redis caching, concurrent processing,
input validation, performance monitoring, and scalability optimizations.
"""

import asyncio
import cv2
import hashlib
import json
import logging
import os
import time
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Optional, Any
import redis
from pydantic import BaseModel, validator

from ..core.config import settings
from .simple_video_search import SearchResult, VideoSearchResponse, ClipResult

logger = logging.getLogger(__name__)

class SearchMetrics(BaseModel):
    """Performance metrics for monitoring"""
    query: str
    video_id: int
    processing_time: float
    method_used: str
    cache_hit: bool
    frames_processed: int
    results_found: int
    timestamp: float

class CachedVideoSearch:
    """
    High-performance video search with caching, concurrency, and monitoring
    """
    
    def __init__(self):
        self.gemini_service = None
        self.native_service = None
        
        # Redis cache setup
        try:
            self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
            self.redis_client.ping()
            self.cache_enabled = True
            logger.info("✅ Redis cache connected for search optimization")
        except Exception as e:
            logger.warning(f"Redis cache unavailable: {e}")
            self.cache_enabled = False
            self.redis_client = None
        
        # Performance settings
        self.max_concurrent_frames = 4  # Process frames concurrently
        self.cache_ttl = settings.CACHE_TTL
        self.max_video_duration = settings.MAX_VIDEO_DURATION_SECONDS
        self.max_query_length = 200
        
        # Thread pool for concurrent processing
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_frames)
        
        # Metrics storage
        self.metrics: List[SearchMetrics] = []
        
    def initialize_services(self, gemini_service, native_service=None):
        """Initialize AI services"""
        self.gemini_service = gemini_service
        self.native_service = native_service
        logger.info("🚀 Cached video search initialized with AI services")
    
    def _validate_search_input(self, video_path: str, query: str, video_id: int) -> bool:
        """Validate search inputs for security and resource limits"""
        
        # Query validation
        if not query or len(query.strip()) == 0:
            raise ValueError("Search query cannot be empty")
        
        if len(query) > self.max_query_length:
            raise ValueError(f"Query too long (max {self.max_query_length} characters)")
        
        # Video validation
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        # Check video duration
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError("Cannot open video file")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        cap.release()
        
        if duration > self.max_video_duration:
            raise ValueError(f"Video too long (max {self.max_video_duration} seconds)")
        
        return True
    
    def _generate_cache_key(self, video_path: str, query: str) -> str:
        """Generate cache key for search results"""
        # Include video file hash and query
        video_hash = hashlib.md5(video_path.encode()).hexdigest()[:8]
        query_hash = hashlib.md5(query.lower().strip().encode()).hexdigest()[:8]
        return f"search:{video_hash}:{query_hash}"
    
    async def _get_cached_results(self, cache_key: str) -> Optional[VideoSearchResponse]:
        """Get cached search results"""
        if not self.cache_enabled:
            return None
        
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"🎯 Cache HIT for search: {cache_key}")
                return VideoSearchResponse(**data)
        except Exception as e:
            logger.warning(f"Cache read error: {e}")
        
        return None
    
    async def _cache_results(self, cache_key: str, response: VideoSearchResponse):
        """Cache search results"""
        if not self.cache_enabled:
            return
        
        try:
            # Convert to dict for JSON serialization
            data = response.dict()
            self.redis_client.setex(
                cache_key, 
                self.cache_ttl, 
                json.dumps(data)
            )
            logger.info(f"💾 Cached search results: {cache_key}")
        except Exception as e:
            logger.warning(f"Cache write error: {e}")
    
    async def _process_frame_concurrent(self, frame_data: tuple) -> Optional[SearchResult]:
        """Process a single frame concurrently with optimized I/O"""
        timestamp, frame, query, video_id = frame_data

        try:
            # OPTIMIZATION: Use in-memory processing with faster compression
            import tempfile
            import io

            # SPEED OPTIMIZATION 1: Resize frame to 320x240 for 4x faster processing
            height, width = frame.shape[:2]
            if width > 320:
                scale = 320 / width
                new_width = 320
                new_height = int(height * scale)
                frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)

            # SPEED OPTIMIZATION 2: Ultra-low quality JPEG for 3x faster encoding
            encode_param = [cv2.IMWRITE_JPEG_QUALITY, 30]  # Reduced from 70 to 30
            success, encoded_img = cv2.imencode('.jpg', frame, encode_param)

            if not success:
                return None

            # Only write to disk if needed by Gemini service
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=True) as tmp:
                tmp.write(encoded_img.tobytes())
                tmp.flush()

                analysis = await self.gemini_service.analyze_frame_for_search(tmp.name, query)

                if analysis.get("match", False):
                    confidence = analysis.get("confidence", 0.5) * 100
                    description = analysis.get("description", f"Found '{query}' at {timestamp:.1f}s")

                    return SearchResult(
                        timestamp=timestamp,
                        confidence=confidence,
                        description=description,
                        frame_path=f"/api/v1/search/{video_id}/frame?timestamp={timestamp}",
                        clip_start=max(0, timestamp - 5),
                        clip_end=timestamp + 10
                    )
        except Exception as e:
            logger.warning(f"Frame processing failed at {timestamp:.1f}s: {e}")

        return None
    
    async def _concurrent_frame_search(self, video_path: str, query: str, video_id: int) -> List[SearchResult]:
        """Process frames concurrently for better performance"""
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        
        # ULTRA-EXTREME SPEED optimization for sub-5-second results
        # Use just 1-2 frames for maximum speed
        if duration <= 1200:  # 20 minutes or less
            sample_interval = duration / 2.0  # Middle of video
            max_frames = 1  # Only 1 frame (MAXIMUM speed)
        else:  # Very long videos
            sample_interval = 300.0  # Every 5 minutes
            max_frames = 2  # Only 2 frames max
        
        # Collect frames for concurrent processing
        frame_data_list = []
        timestamps_to_check = []
        
        for i in range(max_frames):
            timestamp = i * sample_interval
            if timestamp < duration:
                timestamps_to_check.append(timestamp)
        
        logger.info(f"🚀 CONCURRENT: Processing {len(timestamps_to_check)} frames with {self.max_concurrent_frames} workers")
        
        # Extract frames
        for timestamp in timestamps_to_check:
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            if ret:
                frame_data_list.append((timestamp, frame, query, video_id))
        
        cap.release()
        
        # EXTREME SPEED: Process frames with aggressive early termination
        valid_results = []

        # Process frames one by one and stop at first match for speed
        for frame_data in frame_data_list:
            result = await self._process_frame_concurrent(frame_data)
            if result:
                valid_results.append(result)
                # EARLY TERMINATION: Stop after finding just 1 match for speed
                if len(valid_results) >= 1:
                    logger.info(f"⚡ SPEED MODE: Found 1 match, stopping early for sub-5s response")
                    break

        # If no results from early termination, process remaining frames concurrently
        if not valid_results and len(frame_data_list) > 1:
            logger.info("⚡ No early match found, processing remaining frames concurrently")
            remaining_frames = frame_data_list[1:] if len(frame_data_list) > 1 else []
            if remaining_frames:
                tasks = [self._process_frame_concurrent(frame_data) for frame_data in remaining_frames]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                valid_results.extend([r for r in results if isinstance(r, SearchResult)])

        # Sort by confidence and return top results
        valid_results.sort(key=lambda x: x.confidence, reverse=True)
        return valid_results[:3]  # Return top 3 results for speed
    
    async def search(self, video_path: str, query: str, video_id: int) -> VideoSearchResponse:
        """
        High-performance search with caching, validation, and monitoring
        """
        start_time = time.time()
        cache_hit = False
        frames_processed = 0
        
        try:
            # Input validation
            self._validate_search_input(video_path, query, video_id)
            
            # Check cache first
            cache_key = self._generate_cache_key(video_path, query)
            cached_result = await self._get_cached_results(cache_key)
            
            if cached_result:
                cache_hit = True
                processing_time = time.time() - start_time
                
                # Update metrics
                self._record_metrics(query, video_id, processing_time, "cache", cache_hit, 0, len(cached_result.results))
                
                logger.info(f"⚡ CACHED search for '{query}' completed in {processing_time:.3f}s")
                return cached_result
            
            # Perform search with concurrent processing
            results = await self._concurrent_frame_search(video_path, query, video_id)
            frames_processed = len(results)
            
            # Convert to clips
            clips = [ClipResult(
                start_time=r.clip_start,
                end_time=r.clip_end,
                confidence=r.confidence,
                description=r.description,
                thumbnail_url=r.frame_path
            ) for r in results]
            
            processing_time = time.time() - start_time
            
            # Create response
            response = VideoSearchResponse(
                query=query,
                results=results,
                clips=clips,
                total_results=len(results),
                processing_method="concurrent_frame_analysis",
                processing_time=processing_time,
                direct_answer=f"Found {len(results)} instances of '{query}' in the video",
                query_type="visual_search"
            )
            
            # Cache results for future queries
            await self._cache_results(cache_key, response)
            
            # Record metrics
            self._record_metrics(query, video_id, processing_time, "concurrent", cache_hit, frames_processed, len(results))
            
            logger.info(f" CONCURRENT search for '{query}' completed in {processing_time:.2f}s")
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Search failed for '{query}': {e}")
            
            # Record error metrics
            self._record_metrics(query, video_id, processing_time, "error", cache_hit, frames_processed, 0)
            
            return VideoSearchResponse(
                query=query,
                results=[],
                clips=[],
                total_results=0,
                processing_method="error",
                processing_time=processing_time,
                direct_answer=f"Search failed: {str(e)}"
            )
    
    def _record_metrics(self, query: str, video_id: int, processing_time: float, 
                       method: str, cache_hit: bool, frames_processed: int, results_found: int):
        """Record performance metrics"""
        metric = SearchMetrics(
            query=query,
            video_id=video_id,
            processing_time=processing_time,
            method_used=method,
            cache_hit=cache_hit,
            frames_processed=frames_processed,
            results_found=results_found,
            timestamp=time.time()
        )
        
        self.metrics.append(metric)
        
        # Keep only last 1000 metrics
        if len(self.metrics) > 1000:
            self.metrics = self.metrics[-1000:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        if not self.metrics:
            return {"message": "No metrics available"}
        
        recent_metrics = self.metrics[-100:]  # Last 100 searches
        
        avg_time = sum(m.processing_time for m in recent_metrics) / len(recent_metrics)
        cache_hit_rate = sum(1 for m in recent_metrics if m.cache_hit) / len(recent_metrics)
        avg_results = sum(m.results_found for m in recent_metrics) / len(recent_metrics)
        
        return {
            "total_searches": len(self.metrics),
            "recent_searches": len(recent_metrics),
            "average_processing_time": round(avg_time, 2),
            "cache_hit_rate": round(cache_hit_rate * 100, 1),
            "average_results_found": round(avg_results, 1),
            "cache_enabled": self.cache_enabled,
            "concurrent_workers": self.max_concurrent_frames
        }

# Global instance
cached_search = CachedVideoSearch()
