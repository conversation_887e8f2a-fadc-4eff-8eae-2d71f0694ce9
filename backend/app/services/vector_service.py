"""
Simple Vector Service for basic text search functionality
"""
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class VectorService:
    """Simple vector service that provides basic text search without external dependencies"""

    def __init__(self):
        self.available = True
        logger.info("Vector service initialized (simple text-based search)")

    async def add_transcript_embedding(self, video_id: int, transcript: str, metadata: Optional[Dict] = None):
        """Add transcript for simple text search (no actual embedding)"""
        try:
            logger.info(f"Indexing transcript for video {video_id} (simple text search)")
            # In a real implementation, this would create embeddings
            # For now, we just log that the transcript is available for search
            return {"status": "indexed", "method": "simple_text"}
        except Exception as e:
            logger.error(f"Error indexing transcript: {e}")
            return {"status": "error", "error": str(e)}

    async def search_similar(self, query: str, video_id: Optional[int] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Simple text-based search"""
        try:
            logger.info(f"Performing simple text search for: {query}")
            # This would normally do vector similarity search
            # For now, return empty results
            return []
        except Exception as e:
            logger.error(f"Error in vector search: {e}")
            return []

    def get_status(self) -> Dict[str, Any]:
        """Get service status"""
        return {
            "available": self.available,
            "type": "simple_text",
            "features": ["basic_indexing", "text_search"]
        }