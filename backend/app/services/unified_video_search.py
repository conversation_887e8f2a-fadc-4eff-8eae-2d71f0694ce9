"""
Unified Video Search Service

This service consolidates and replaces both simple_video_search.py and cached_video_search.py
to eliminate redundancies and provide a single, comprehensive video search solution.

Features:
- Native Gemini 2.5 video understanding (primary method)
- Concurrent frame analysis with caching (fallback method)
- Redis caching for performance optimization
- Input validation and resource limits
- Performance monitoring and metrics
- Configurable sampling strategies
- Automatic fallback handling
"""

import asyncio
import cv2
import tempfile
import os
import time
import logging
import hashlib
import json
from concurrent.futures import ThreadPoolExecutor
from typing import List, Optional, Dict, Any
import redis
from pydantic import BaseModel

# SearchResult is defined below
from .gemini_service import GeminiService
from .native_video_search import NativeVideoSearchService
from ..core.config import settings

logger = logging.getLogger(__name__)

class SearchResult(BaseModel):
    timestamp: float
    confidence: float
    description: str
    frame_path: str
    clip_start: float
    clip_end: float

class ClipResult(BaseModel):
    start_time: float
    end_time: float
    confidence: float
    description: str
    thumbnail_url: Optional[str] = None

class VideoSearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    clips: List[ClipResult] = []
    total_results: int
    processing_method: str
    processing_time: float
    direct_answer: Optional[str] = None
    query_type: Optional[str] = None

class SearchMetrics(BaseModel):
    """Performance metrics for monitoring"""
    query: str
    video_id: int
    processing_time: float
    method_used: str
    cache_hit: bool
    frames_processed: int
    results_found: int
    timestamp: float

class UnifiedVideoSearch:
    """
    Unified video search service that consolidates all search capabilities
    """
    
    def __init__(self, enable_cache: bool = True, enable_concurrency: bool = True):
        self.gemini_service = None
        self.native_service = None
        
        # Configuration
        self.enable_cache = enable_cache
        self.enable_concurrency = enable_concurrency
        self.max_concurrent_frames = 8 if enable_concurrency else 1  # OPTIMIZED: Increased from 4 to 8
        self.max_video_duration = getattr(settings, 'MAX_VIDEO_DURATION_SECONDS', 3600)
        self.max_query_length = 200
        
        # Redis cache setup
        self.cache_enabled = False
        self.redis_client = None
        if enable_cache:
            try:
                self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
                self.redis_client.ping()
                self.cache_enabled = True
                self.cache_ttl = getattr(settings, 'CACHE_TTL', 3600)
                logger.info("✅ Redis cache connected for unified search")
            except Exception as e:
                logger.warning(f"Redis cache unavailable: {e}")
                self.cache_enabled = False
        
        # Thread pool for concurrent processing
        if enable_concurrency:
            self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_frames)
        
        # Metrics storage
        self.metrics: List[SearchMetrics] = []
        
        logger.info(f"🚀 Unified video search initialized (cache: {self.cache_enabled}, concurrency: {enable_concurrency})")
    
    def initialize_services(self, gemini_service, native_service=None):
        """Initialize AI services"""
        self.gemini_service = gemini_service
        self.native_service = native_service
        logger.info("✅ Unified search services initialized")
    
    def _validate_input(self, video_path: str, query: str) -> bool:
        """Centralized input validation"""
        
        # Query validation
        if not query or len(query.strip()) == 0:
            raise ValueError("Search query cannot be empty")
        
        if len(query) > self.max_query_length:
            raise ValueError(f"Query too long (max {self.max_query_length} characters)")
        
        # Video validation
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        # Check video duration
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError("Cannot open video file")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        cap.release()
        
        if duration > self.max_video_duration:
            raise ValueError(f"Video too long (max {self.max_video_duration} seconds)")
        
        return True
    
    def _generate_cache_key(self, video_path: str, query: str) -> str:
        """Generate cache key for search results"""
        video_hash = hashlib.md5(video_path.encode()).hexdigest()[:8]
        query_hash = hashlib.md5(query.lower().strip().encode()).hexdigest()[:8]
        return f"unified_search:{video_hash}:{query_hash}"
    
    async def _get_cached_results(self, cache_key: str) -> Optional[VideoSearchResponse]:
        """Get cached search results"""
        if not self.cache_enabled:
            return None
        
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"🎯 Cache HIT: {cache_key}")
                return VideoSearchResponse(**data)
        except Exception as e:
            logger.warning(f"Cache read error: {e}")
        
        return None
    
    async def _cache_results(self, cache_key: str, response: VideoSearchResponse):
        """Cache search results"""
        if not self.cache_enabled:
            return
        
        try:
            data = response.model_dump()
            self.redis_client.setex(cache_key, self.cache_ttl, json.dumps(data))
            logger.info(f"💾 Cached results: {cache_key}")
        except Exception as e:
            logger.warning(f"Cache write error: {e}")
    
    def _get_sampling_strategy(self, duration: float) -> tuple:
        """Ultra-fast sampling strategy for sub-second search responses"""
        
        # OPTIMIZED: Dramatically reduced sampling for speed
        # We rely on native search for accuracy, frame search is just fallback
        if duration <= 180:  # 3 minutes or less
            sample_interval = 30.0  # Every 30 seconds
            max_frames = 3  # Only 3 frames max (was 8)
        elif duration <= 600:  # 10 minutes or less
            sample_interval = 45.0  # Every 45 seconds
            max_frames = 5  # Only 5 frames max (was 12)
        else:  # Longer videos
            sample_interval = 60.0  # Every 60 seconds
            max_frames = 8  # Only 8 frames max (was 15)
        
        logger.info(f"⚡ ULTRA-FAST sampling: {max_frames} frames at {sample_interval}s intervals")
        return sample_interval, max_frames
    
    async def _native_search(self, video_path: str, query: str) -> List[SearchResult]:
        """Use native Gemini 2.5 video understanding"""
        if not self.native_service:
            return []
        
        try:
            # Determine search type for optimal results
            query_lower = query.lower()
            if any(word in query_lower for word in ['how many', 'count', 'number of']):
                search_type = "counting"
            elif any(word in query_lower for word in ['red', 'blue', 'green', 'yellow', 'color']):
                search_type = "color"
            elif any(word in query_lower for word in ['text', 'sign', 'writing']):
                search_type = "text"
            else:
                search_type = "object"
            
            # Get clips from native search
            clips = await self.native_service.search_visual_content(
                video_path=video_path,
                query=query,
                search_type=search_type
            )
            
            # Convert to SearchResult format
            results = []
            for clip in clips:
                results.append(SearchResult(
                    timestamp=clip.start_time,
                    confidence=clip.confidence,
                    description=clip.description,
                    frame_path=f"/api/search/frame?video_path={video_path}&timestamp={clip.start_time}",
                    clip_start=clip.start_time,
                    clip_end=clip.end_time
                ))
            
            return results
            
        except Exception as e:
            logger.error(f"Native search failed: {e}")
            return []
    
    async def _process_frame_concurrent(self, frame_data: tuple) -> Optional[SearchResult]:
        """Process a single frame (optimized for concurrency)"""
        timestamp, frame, query, video_id = frame_data
        
        try:
            # OPTIMIZED: Lower quality for 2x faster encoding
            encode_param = [cv2.IMWRITE_JPEG_QUALITY, 50]  # Reduced from 70 to 50
            success, encoded_img = cv2.imencode('.jpg', frame, encode_param)
            
            if not success:
                return None
            
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=True) as tmp:
                tmp.write(encoded_img.tobytes())
                tmp.flush()
                
                analysis = await self.gemini_service.analyze_frame_for_search(tmp.name, query)
                
                if analysis.get("match", False):
                    confidence = analysis.get("confidence", 0.5) * 100
                    description = analysis.get("description", f"Found '{query}' at {timestamp:.1f}s")
                    
                    return SearchResult(
                        timestamp=timestamp,
                        confidence=confidence,
                        description=description,
                        frame_path=f"/api/v1/search/{video_id}/frame?timestamp={timestamp}",
                        clip_start=max(0, timestamp - 5),
                        clip_end=timestamp + 10
                    )
        except Exception as e:
            logger.warning(f"Frame processing failed at {timestamp:.1f}s: {e}")
        
        return None
    
    async def _frame_search(self, video_path: str, query: str, video_id: int) -> List[SearchResult]:
        """Unified frame search with optional concurrency"""
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        
        # Use unified sampling strategy
        sample_interval, max_frames = self._get_sampling_strategy(duration)
        
        # Collect frames
        frame_data_list = []
        timestamps_to_check = []
        
        for i in range(max_frames):
            timestamp = i * sample_interval
            if timestamp < duration:
                timestamps_to_check.append(timestamp)
        
        logger.info(f"🚀 UNIFIED: Processing {len(timestamps_to_check)} frames at {sample_interval}s intervals")
        
        # Extract frames
        for timestamp in timestamps_to_check:
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            if ret:
                frame_data_list.append((timestamp, frame, query, video_id))
        
        cap.release()
        
        # OPTIMIZED: Process frames with maximum concurrency
        if self.enable_concurrency and len(frame_data_list) > 1:
            # Create all tasks at once for true parallel processing
            tasks = []
            for frame_data in frame_data_list:
                task = asyncio.create_task(self._process_frame_concurrent(frame_data))
                tasks.append(task)
            
            # Process all frames concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            valid_results = [r for r in results if isinstance(r, SearchResult)]
            
            logger.info(f"⚡ Processed {len(frame_data_list)} frames concurrently, found {len(valid_results)} matches")
        else:
            # Sequential processing (fallback)
            valid_results = []
            for frame_data in frame_data_list:
                result = await self._process_frame_concurrent(frame_data)
                if result:
                    valid_results.append(result)
        
        # Sort by confidence and limit results
        valid_results.sort(key=lambda x: x.confidence, reverse=True)
        return valid_results[:5]  # Return top 5 results for speed

    def _results_to_clips(self, results: List[SearchResult]) -> List[ClipResult]:
        """Convert search results to clips for timeline/clips view"""
        clips = []
        for result in results:
            clips.append(ClipResult(
                start_time=result.clip_start,
                end_time=result.clip_end,
                confidence=result.confidence,
                description=result.description,
                thumbnail_url=result.frame_path
            ))
        return clips

    async def search(self, video_path: str, query: str, video_id: int, use_cache: bool = True) -> VideoSearchResponse:
        """
        Unified search method that handles all search capabilities
        """
        start_time = time.time()
        cache_hit = False
        frames_processed = 0

        try:
            # Input validation
            self._validate_input(video_path, query)

            # Check cache first (if enabled and requested)
            cached_result = None
            cache_key = None
            if use_cache and self.cache_enabled:
                cache_key = self._generate_cache_key(video_path, query)
                cached_result = await self._get_cached_results(cache_key)

                if cached_result:
                    cache_hit = True
                    processing_time = time.time() - start_time

                    # Update metrics
                    self._record_metrics(query, video_id, processing_time, "cache", cache_hit, 0, len(cached_result.results))

                    logger.info(f"⚡ CACHED search for '{query}' completed in {processing_time:.3f}s")
                    return cached_result

            # METHOD 1: Try native search first
            results = []
            method_used = "no_results"

            if self.native_service:
                try:
                    results = await self._native_search(video_path, query)
                    if results:
                        method_used = "native_video_search"
                        logger.info(f"✅ Native search found {len(results)} results")
                except Exception as e:
                    logger.warning(f"Native search failed, falling back to frame analysis: {e}")

            # METHOD 2: Fallback to frame search if native failed or unavailable
            if not results and self.gemini_service:
                try:
                    results = await self._frame_search(video_path, query, video_id)
                    frames_processed = len(results)
                    if results:
                        method_used = "concurrent_frame_analysis" if self.enable_concurrency else "sequential_frame_analysis"
                        logger.info(f"✅ Frame search found {len(results)} results")
                except Exception as e:
                    logger.error(f"Frame search failed: {e}")
                    method_used = "error"

            # Convert to clips
            clips = self._results_to_clips(results)

            processing_time = time.time() - start_time

            # Create response
            response = VideoSearchResponse(
                query=query,
                results=results,
                clips=clips,
                total_results=len(results),
                processing_method=method_used,
                processing_time=processing_time,
                direct_answer=f"Found {len(results)} instances of '{query}' in the video" if results else f"No instances of '{query}' found",
                query_type="visual_search"
            )

            # Cache results for future queries (if enabled and successful)
            if use_cache and self.cache_enabled and cache_key and results:
                await self._cache_results(cache_key, response)

            # Record metrics
            self._record_metrics(query, video_id, processing_time, method_used, cache_hit, frames_processed, len(results))

            logger.info(f"🚀 UNIFIED search for '{query}' completed in {processing_time:.2f}s using {method_used}")
            return response

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Unified search failed for '{query}': {e}")

            # Record error metrics
            self._record_metrics(query, video_id, processing_time, "error", cache_hit, frames_processed, 0)

            return VideoSearchResponse(
                query=query,
                results=[],
                clips=[],
                total_results=0,
                processing_method="error",
                processing_time=processing_time,
                direct_answer=f"Search failed: {str(e)}",
                query_type="visual_search"
            )

    def _record_metrics(self, query: str, video_id: int, processing_time: float,
                       method: str, cache_hit: bool, frames_processed: int, results_found: int):
        """Record performance metrics"""
        metric = SearchMetrics(
            query=query,
            video_id=video_id,
            processing_time=processing_time,
            method_used=method,
            cache_hit=cache_hit,
            frames_processed=frames_processed,
            results_found=results_found,
            timestamp=time.time()
        )

        self.metrics.append(metric)

        # Keep only last 1000 metrics
        if len(self.metrics) > 1000:
            self.metrics = self.metrics[-1000:]

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        if not self.metrics:
            return {"message": "No metrics available"}

        recent_metrics = self.metrics[-100:]  # Last 100 searches

        avg_time = sum(m.processing_time for m in recent_metrics) / len(recent_metrics)
        cache_hit_rate = sum(1 for m in recent_metrics if m.cache_hit) / len(recent_metrics)
        avg_results = sum(m.results_found for m in recent_metrics) / len(recent_metrics)

        # Method breakdown
        method_counts = {}
        for m in recent_metrics:
            method_counts[m.method_used] = method_counts.get(m.method_used, 0) + 1

        return {
            "total_searches": len(self.metrics),
            "recent_searches": len(recent_metrics),
            "average_processing_time": round(avg_time, 2),
            "cache_hit_rate": round(cache_hit_rate * 100, 1),
            "average_results_found": round(avg_results, 1),
            "cache_enabled": self.cache_enabled,
            "concurrency_enabled": self.enable_concurrency,
            "concurrent_workers": self.max_concurrent_frames,
            "method_breakdown": method_counts,
            "configuration": {
                "max_video_duration": self.max_video_duration,
                "max_query_length": self.max_query_length,
                "cache_ttl": getattr(self, 'cache_ttl', 0)
            }
        }

# Global instance with optimal configuration
unified_search = UnifiedVideoSearch(enable_cache=True, enable_concurrency=True)
