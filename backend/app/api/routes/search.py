"""
Simple Search Routes - Original Implementation
Basic visual search using vector service + Gemini analysis
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
import os

from app.core.database import get_db
from app.models.video import Video, VideoFrame
from app.services.vector_service import get_vector_service
from app.services.gemini_service import GeminiService
from app.services.query_analyzer import QueryAnalyzer

logger = logging.getLogger(__name__)
router = APIRouter()

class VisualSearchRequest(BaseModel):
    query: str
    video_id: Optional[int] = None
    limit: int = 10

class SearchResult(BaseModel):
    timestamp: float
    confidence: float
    description: str
    frame_path: str
    metadata: Optional[Dict[str, Any]] = None

class VisualSearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total_results: int
    method: str

@router.post("/visual", response_model=VisualSearchResponse)
async def visual_search(
    request: VisualSearchRequest,
    db: Session = Depends(get_db)
):
    """Enhanced visual search using query analysis and vector service"""
    try:
        logger.info(f"🔍 Visual search for: '{request.query}'")
        
        # Analyze query for better search strategy
        query_analyzer = QueryAnalyzer()
        analysis = query_analyzer.analyze_query(request.query)
        
        logger.info(f"Query analysis: type={analysis.query_type}, method={analysis.suggested_method}, confidence={analysis.confidence}")
        
        # Get vector service
        vector_service = get_vector_service()
        if not vector_service.initialized:
            raise HTTPException(
                status_code=503, 
                detail="Vector search service not available"
            )
        
        # Use enhanced query for better results
        search_query = analysis.enhanced_query
        logger.info(f"Enhanced query: '{search_query}'")
        
        # Search frames
        search_results = await vector_service.search_frames(
            query=search_query,
            video_id=request.video_id,
            limit=request.limit
        )
        
        # Convert to SearchResult format
        results = []
        for result in search_results:
            results.append(SearchResult(
                timestamp=result["timestamp"],
                confidence=result["relevance_score"] * 100,
                description=result["description"],
                frame_path=result["frame_path"],
                metadata=result["metadata"]
            ))
        
        logger.info(f"✅ Found {len(results)} results for '{request.query}' using {analysis.suggested_method}")
        
        return VisualSearchResponse(
            query=request.query,
            results=results,
            total_results=len(results),
            method=f"enhanced_{analysis.suggested_method}"
        )
        
    except Exception as e:
        logger.error(f"Visual search failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/frames")
async def get_video_frames(
    video_id: int,
    db: Session = Depends(get_db)
):
    """Get frames for a video"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    frames = db.query(VideoFrame)\
        .filter(VideoFrame.video_id == video_id)\
        .order_by(VideoFrame.timestamp)\
        .all()
    
    return {
        "video_id": video_id,
        "total_frames": len(frames),
        "frames": [{
            "id": frame.id,
            "timestamp": frame.timestamp,
            "frame_path": frame.frame_path,
            "description": frame.description
        } for frame in frames]
    }

@router.post("/{video_id}/analyze-frames")
async def analyze_video_frames(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Analyze video frames with Gemini and add to vector search"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Get frames that need analysis
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.is_(None))\
            .limit(50)\
            .all()
        
        if not frames:
            return {
                "message": "All frames already analyzed",
                "analyzed_count": 0
            }
        
        # Get services
        gemini_service = request.app.state.gemini_service
        vector_service = get_vector_service()
        
        analyzed_count = 0
        
        for frame in frames:
            try:
                # Analyze frame with Gemini
                analysis = await gemini_service.analyze_frame(
                    frame.frame_path,
                    context=f"Frame from video: {video.title}"
                )
                
                if analysis["status"] == "success":
                    frame.description = analysis["description"]
                    
                    # Add to vector search
                    if vector_service.initialized:
                        await vector_service.add_frame(
                            frame_id=f"frame_{frame.id}",
                            description=analysis["description"],
                            timestamp=frame.timestamp,
                            video_id=video_id,
                            frame_path=frame.frame_path,
                            metadata={"video_id": video_id}
                        )
                    
                    analyzed_count += 1
                    
            except Exception as e:
                logger.error(f"Error analyzing frame {frame.id}: {e}")
                continue
        
        db.commit()
        
        # Update video status
        if analyzed_count > 0:
            video.embedding_status = "completed"
            db.commit()
        
        return {
            "message": f"Analyzed {analyzed_count} frames",
            "analyzed_count": analyzed_count,
            "total_frames": len(frames)
        }
        
    except Exception as e:
        logger.error(f"Frame analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_search_stats():
    """Get search service statistics"""
    vector_service = get_vector_service()
    return vector_service.get_stats()